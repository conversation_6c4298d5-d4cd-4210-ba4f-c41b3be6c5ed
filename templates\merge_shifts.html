{% extends "base.html" %}

{% block title %}Merge Shifts{% endblock %}

{% block extra_css %}
<style>
    /* Merge Shifts Page Styles */
    .merge-shifts-content {
        min-height: 100vh;
        padding: 2rem;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }

    .modern-card {
        background: rgba(255, 255, 255, 0.15);
        backdrop-filter: blur(25px);
        -webkit-backdrop-filter: blur(25px);
        border: 2px solid rgba(255, 255, 255, 0.3);
        border-radius: 20px;
        box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
        overflow: hidden;
    }

    .modern-card-header {
        padding: 1.5rem 2rem;
        border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        background: rgba(255, 255, 255, 0.1);
    }

    .modern-card-body {
        padding: 2rem;
    }

    .card-title {
        color: white;
        font-weight: 600;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    }

    .form-label-glass {
        color: rgba(255, 255, 255, 0.9);
        font-weight: 500;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
        margin-bottom: 0.5rem;
        display: block;
    }

    .form-control-glass {
        background: rgba(255, 255, 255, 0.1);
        border: 2px solid rgba(255, 255, 255, 0.2);
        border-radius: 12px;
        color: white;
        padding: 0.75rem 1rem;
        font-size: 0.95rem;
        transition: all 0.3s ease;
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
    }

    .form-control-glass:focus {
        background: rgba(255, 255, 255, 0.15);
        border-color: rgba(255, 255, 255, 0.5);
        box-shadow: 0 0 20px rgba(255, 255, 255, 0.2);
        outline: none;
        color: white;
    }

    .form-control-glass::placeholder {
        color: rgba(255, 255, 255, 0.6);
    }

    /* Shifts Selection */
    .shifts-selection {
        max-height: 400px;
        overflow-y: auto;
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 12px;
        padding: 1rem;
        background: rgba(255, 255, 255, 0.05);
    }

    .shift-checkbox-item {
        margin-bottom: 0.75rem;
    }

    .shift-checkbox {
        display: none;
    }

    .shift-checkbox-label {
        display: block;
        padding: 1rem;
        border: 2px solid rgba(255, 255, 255, 0.2);
        border-radius: 12px;
        background: rgba(255, 255, 255, 0.05);
        cursor: pointer;
        transition: all 0.3s ease;
        color: white;
    }

    .shift-checkbox:checked + .shift-checkbox-label {
        border-color: #4ade80;
        background: rgba(74, 222, 128, 0.15);
        box-shadow: 0 0 20px rgba(74, 222, 128, 0.3);
    }

    .shift-checkbox-label:hover {
        border-color: rgba(255, 255, 255, 0.4);
        background: rgba(255, 255, 255, 0.1);
    }

    .shift-info {
        display: grid;
        grid-template-columns: 1fr auto;
        gap: 0.5rem;
        align-items: center;
    }

    .shift-info strong {
        grid-column: 1 / -1;
        font-size: 1.1rem;
        margin-bottom: 0.25rem;
    }

    .shift-time {
        color: #3b82f6;
        font-weight: 500;
    }

    .shift-price {
        color: #10b981;
        font-weight: 600;
    }

    .shift-duration {
        color: #f59e0b;
        font-weight: 500;
        font-size: 0.9rem;
    }

    /* Merge Preview */
    .merge-preview {
        background: rgba(59, 130, 246, 0.15);
        border: 1px solid rgba(59, 130, 246, 0.4);
        border-radius: 12px;
        padding: 1rem;
        margin-top: 1rem;
    }

    .merge-preview h6 {
        color: #3b82f6;
        margin-bottom: 0.75rem;
    }

    .merge-preview-item {
        background: rgba(255, 255, 255, 0.1);
        border-radius: 8px;
        padding: 0.75rem;
        margin-bottom: 0.5rem;
        color: white;
    }

    /* Buttons */
    .btn-glass-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: 2px solid rgba(255, 255, 255, 0.3);
        border-radius: 12px;
        color: white;
        padding: 0.75rem 1.5rem;
        font-weight: 600;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        transition: all 0.3s ease;
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
    }

    .btn-glass-secondary {
        background: rgba(255, 255, 255, 0.1);
        border: 2px solid rgba(255, 255, 255, 0.3);
        border-radius: 12px;
        color: white;
        padding: 0.75rem 1.5rem;
        font-weight: 600;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        transition: all 0.3s ease;
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
    }

    .btn-glass-primary:hover,
    .btn-glass-secondary:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
        color: white;
        text-decoration: none;
    }

    .help-text {
        color: rgba(255, 255, 255, 0.6);
        font-size: 0.75rem;
        margin-top: 0.25rem;
        display: flex;
        align-items: center;
        gap: 0.25rem;
    }

    .price-input-container {
        position: relative;
        display: flex;
        align-items: center;
    }

    .billing-cycle-indicator {
        position: absolute;
        right: 1rem;
        top: 50%;
        transform: translateY(-50%);
        color: rgba(255, 255, 255, 0.8);
        font-weight: 600;
        font-size: 0.9rem;
        pointer-events: none;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
        background: rgba(0, 0, 0, 0.2);
        padding: 0.25rem 0.5rem;
        border-radius: 6px;
        backdrop-filter: blur(5px);
        -webkit-backdrop-filter: blur(5px);
    }

    /* Alert Messages */
    .alert-glass {
        background: rgba(255, 255, 255, 0.15);
        border: 1px solid rgba(255, 255, 255, 0.3);
        border-radius: 12px;
        color: white;
        padding: 1rem;
        margin-bottom: 1rem;
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
    }

    .alert-success {
        border-color: rgba(74, 222, 128, 0.5);
        background: rgba(74, 222, 128, 0.15);
    }

    .alert-danger {
        border-color: rgba(239, 68, 68, 0.5);
        background: rgba(239, 68, 68, 0.15);
    }

    .alert-warning {
        border-color: rgba(245, 158, 11, 0.5);
        background: rgba(245, 158, 11, 0.15);
    }

    /* Responsive */
    @media (max-width: 768px) {
        .merge-shifts-content {
            padding: 1rem;
        }

        .modern-card-body {
            padding: 1.5rem;
        }

        .shift-info {
            grid-template-columns: 1fr;
        }

        .shift-info strong {
            grid-column: 1;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="merge-shifts-content">
    <!-- Alert Messages -->
    {% if messages %}
        {% for message in messages %}
            <div class="alert-glass alert-{{ message.tags|default:'primary' }} alert-dismissible fade show mb-4" role="alert">
                <i class="fas fa-{% if message.tags == 'success' %}check-circle{% elif message.tags == 'error' or message.tags == 'danger' %}exclamation-triangle{% elif message.tags == 'warning' %}exclamation-circle{% else %}info-circle{% endif %} me-2"></i>
                {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        {% endfor %}
    {% endif %}

    <div class="container-fluid">
        <div class="row justify-content-center">
            <div class="col-lg-8 col-md-10">
                <div class="modern-card">
                    <div class="modern-card-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h4 class="card-title mb-0">
                                <i class="fas fa-compress-arrows-alt me-2"></i>Merge Shifts
                            </h4>
                            <a href="/{{ role }}/shifts/" class="btn-glass-secondary">
                                <i class="fas fa-arrow-left me-2"></i>Back to Shifts
                            </a>
                        </div>
                    </div>
                    <div class="modern-card-body">
                        {% if shifts|length < 2 %}
                            <div class="text-center text-white">
                                <i class="fas fa-info-circle fa-3x mb-3" style="color: rgba(255, 255, 255, 0.6);"></i>
                                <h5>Not Enough Shifts</h5>
                                <p>You need at least 2 shifts to perform a merge operation.</p>
                                <a href="/{{ role }}/shifts/" class="btn-glass-primary">
                                    <i class="fas fa-plus me-2"></i>Create More Shifts
                                </a>
                            </div>
                        {% else %}
                            <form method="post" id="mergeShiftsForm">
                                {% csrf_token %}
                                <div class="mb-4">
                                    <label for="merged_name" class="form-label-glass">
                                        <i class="fas fa-tag me-2"></i>Merged Shift Name
                                    </label>
                                    <input type="text" class="form-control-glass" id="merged_name" name="merged_name" 
                                           placeholder="e.g., Extended Day Shift" required>
                                    <div class="help-text mt-1">
                                        Enter a descriptive name for the new merged shift
                                    </div>
                                </div>

                                <div class="mb-4">
                                    <label class="form-label-glass">
                                        <i class="fas fa-check-square me-2"></i>Select Shifts to Merge
                                    </label>
                                    <div class="shifts-selection">
                                        {% for shift in shifts %}
                                        <div class="shift-checkbox-item">
                                            <input type="checkbox" id="shift_{{ shift.id }}" name="shift_ids" value="{{ shift.id }}" class="shift-checkbox">
                                            <label for="shift_{{ shift.id }}" class="shift-checkbox-label">
                                                <div class="shift-info">
                                                    <strong>{{ shift.name }}</strong>
                                                    <span class="shift-time">{{ shift.formatted_time_range }}</span>
                                                    <span class="shift-price">₹{{ shift.price }}/{{ librarian.get_default_billing_cycle_display|default:'month'|lower }}</span>
                                                    <span class="shift-duration">{{ shift.get_duration_hours|floatformat:1 }}h</span>
                                                </div>
                                            </label>
                                        </div>
                                        {% endfor %}
                                    </div>
                                    <div class="help-text mt-1">
                                        Select 2 or more shifts to merge. The system will combine them into a single shift.
                                    </div>
                                </div>

                                <div class="mb-4">
                                    <label for="merged_price" class="form-label-glass">
                                        <i class="fas fa-rupee-sign me-2"></i>Merged Shift Price (₹)
                                    </label>
                                    <div class="price-input-container">
                                        <input type="number" step="0.01" min="0" class="form-control-glass" 
                                               id="merged_price" name="merged_price" placeholder="0.00" required>
                                        <div class="billing-cycle-indicator">
                                            /{{ librarian.get_default_billing_cycle_display|default:'month'|lower }}
                                        </div>
                                    </div>
                                    <div class="help-text mt-1">
                                        <span id="suggestedPrice">Select shifts to see suggested price</span>
                                    </div>
                                </div>

                                <div id="mergePreview" class="merge-preview" style="display: none;">
                                    <h6><i class="fas fa-eye me-2"></i>Merge Preview</h6>
                                    <div id="previewContent"></div>
                                </div>

                                <div class="d-flex gap-3 justify-content-end">
                                    <a href="/{{ role }}/shifts/" class="btn-glass-secondary">
                                        <i class="fas fa-times me-2"></i>Cancel
                                    </a>
                                    <button type="submit" class="btn-glass-primary" id="mergeBtn">
                                        <i class="fas fa-compress-arrows-alt me-2"></i>Merge Shifts
                                    </button>
                                </div>
                            </form>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const checkboxes = document.querySelectorAll('.shift-checkbox');
        const priceInput = document.getElementById('merged_price');
        const form = document.getElementById('mergeShiftsForm');

        // Setup event listeners
        checkboxes.forEach(checkbox => {
            checkbox.addEventListener('change', updateMergePreview);
        });

        if (priceInput) {
            priceInput.addEventListener('input', updateMergePreview);
        }

        // Form submission
        if (form) {
            form.addEventListener('submit', function(e) {
                const selectedShifts = getSelectedShifts();
                const mergedName = document.getElementById('merged_name').value.trim();
                const mergedPrice = document.getElementById('merged_price').value;

                if (selectedShifts.length < 2) {
                    e.preventDefault();
                    alert('Please select at least 2 shifts to merge');
                    return false;
                }

                if (!mergedName) {
                    e.preventDefault();
                    alert('Please enter a name for the merged shift');
                    return false;
                }

                if (!mergedPrice || parseFloat(mergedPrice) <= 0) {
                    e.preventDefault();
                    alert('Please enter a valid price for the merged shift');
                    return false;
                }

                // Show loading state
                const submitBtn = document.getElementById('mergeBtn');
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Merging...';
                submitBtn.disabled = true;

                return true;
            });
        }
    });

    function updateMergePreview() {
        const selectedShifts = getSelectedShifts();
        const preview = document.getElementById('mergePreview');
        const previewContent = document.getElementById('previewContent');
        const suggestedPrice = document.getElementById('suggestedPrice');

        if (selectedShifts.length < 2) {
            preview.style.display = 'none';
            suggestedPrice.textContent = 'Select at least 2 shifts to see suggested price';
            return;
        }

        // Calculate suggested price and time range
        let totalDuration = 0;
        let totalWeightedPrice = 0;
        let earliestStart = null;
        let latestEnd = null;

        selectedShifts.forEach(shift => {
            const duration = parseFloat(shift.duration);
            const price = parseFloat(shift.price);

            totalDuration += duration;
            totalWeightedPrice += price * duration;

            // Parse time strings for comparison
            const startTime = shift.startTime;
            const endTime = shift.endTime;

            if (!earliestStart || startTime < earliestStart) {
                earliestStart = startTime;
            }
            if (!latestEnd || endTime > latestEnd) {
                latestEnd = endTime;
            }
        });

        const suggestedPriceValue = totalDuration > 0 ? (totalWeightedPrice / totalDuration).toFixed(2) : 0;

        // Update suggested price
        suggestedPrice.innerHTML = `
            <i class="fas fa-lightbulb me-1"></i>
            Suggested: ₹${suggestedPriceValue} (based on duration-weighted average)
        `;

        // Update preview
        let previewHtml = `
            <div class="merge-preview-item">
                <strong>Selected Shifts:</strong> ${selectedShifts.map(s => s.name).join(', ')}
            </div>
            <div class="merge-preview-item">
                <strong>Time Range:</strong> ${earliestStart} - ${latestEnd}
                ${isOvernightRange(earliestStart, latestEnd) ? ' <span style="color: #f59e0b;">(Overnight)</span>' : ''}
            </div>
            <div class="merge-preview-item">
                <strong>Total Duration:</strong> ${totalDuration.toFixed(1)} hours
            </div>
            <div class="merge-preview-item">
                <strong>Individual Prices:</strong> ${selectedShifts.map(s => `₹${s.price}`).join(', ')}
            </div>
        `;

        previewContent.innerHTML = previewHtml;
        preview.style.display = 'block';
    }

    function getSelectedShifts() {
        const checkboxes = document.querySelectorAll('.shift-checkbox:checked');
        const shifts = [];

        checkboxes.forEach(checkbox => {
            const label = checkbox.nextElementSibling;
            const shiftInfo = label.querySelector('.shift-info');
            const name = shiftInfo.querySelector('strong').textContent;
            const timeRange = shiftInfo.querySelector('.shift-time').textContent;
            const priceText = shiftInfo.querySelector('.shift-price').textContent;
            const durationText = shiftInfo.querySelector('.shift-duration').textContent;

            // Parse time range
            const [startTime, endTime] = timeRange.split(' - ').map(t => t.replace(' (+1 day)', ''));

            // Parse price (remove ₹ and /month)
            const price = priceText.replace(/[₹,]/g, '').split('/')[0];

            // Parse duration (remove 'h')
            const duration = durationText.replace('h', '');

            shifts.push({
                id: checkbox.value,
                name: name,
                startTime: startTime,
                endTime: endTime,
                price: price,
                duration: duration,
                isOvernight: timeRange.includes('(+1 day)')
            });
        });

        return shifts;
    }

    function isOvernightRange(startTime, endTime) {
        const start = new Date(`2000-01-01T${startTime}:00`);
        const end = new Date(`2000-01-01T${endTime}:00`);
        return end <= start;
    }
</script>
{% endblock %}
